import { atom, map } from 'nanostores';
import { pb } from './pocketbase.ts';

// User authentication state
export const userStore = atom(null);
export const isAuthenticatedStore = atom(false);
export const authLoadingStore = atom(true);

// UI state
export const themeStore = atom('light');
export const sidebarOpenStore = atom(false);
export const notificationsStore = atom([]);

// Search state
export const searchQueryStore = atom('');
export const searchResultsStore = atom([]);
export const searchLoadingStore = atom(false);
export const searchFiltersStore = map({
  minPrice: null,
  maxPrice: null,
  minCapacity: null,
  amenities: [],
  location: null,
  sort: 'relevance'
});

// Booking state
export const activeBookingsStore = atom([]);
export const bookingLoadingStore = atom(false);

// Venue state
export const userVenuesStore = atom([]);
export const venueLoadingStore = atom(false);

// Initialize authentication state
export function initializeAuth() {
  authLoadingStore.set(true);
  
  try {
    // Check if user is already authenticated
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model);
      isAuthenticatedStore.set(true);
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);
    }
  } catch (error) {
    console.error('Failed to initialize auth state:', error);
    userStore.set(null);
    isAuthenticatedStore.set(false);
  } finally {
    authLoadingStore.set(false);
  }
}

// Authentication actions
export const authActions = {
  async login(email, password) {
    try {
      authLoadingStore.set(true);
      const authData = await pb.collection('users').authWithPassword(email, password);
      
      userStore.set(authData.record);
      isAuthenticatedStore.set(true);
      
      return { success: true, user: authData.record };
    } catch (error) {
      console.error('Login failed:', error);
      return { success: false, error: error.message };
    } finally {
      authLoadingStore.set(false);
    }
  },

  async register(userData) {
    try {
      authLoadingStore.set(true);
      
      // Create user account
      const _user = await pb.collection('users').create({
        ...userData,
        roles: ['renter'] // Default role
      });

      // Send verification email
      await pb.collection('users').requestVerification(userData.email);

      // Auto-login after registration
      const authData = await pb.collection('users').authWithPassword(
        userData.email,
        userData.password
      );
      
      userStore.set(authData.record);
      isAuthenticatedStore.set(true);
      
      return { success: true, user: authData.record };
    } catch (error) {
      console.error('Registration failed:', error);
      return { success: false, error: error.message };
    } finally {
      authLoadingStore.set(false);
    }
  },

  logout() {
    pb.authStore.clear();
    userStore.set(null);
    isAuthenticatedStore.set(false);
    
    // Clear other user-specific state
    activeBookingsStore.set([]);
    userVenuesStore.set([]);
    
    // Redirect to home page
    if (typeof globalThis !== 'undefined' && globalThis.window) {
      globalThis.window.location.href = '/';
    }
  },

  async refreshAuth() {
    try {
      if (pb.authStore.isValid) {
        await pb.collection('users').authRefresh();
        userStore.set(pb.authStore.model);
        isAuthenticatedStore.set(true);
        return { success: true };
      }
    } catch (error) {
      console.error('Auth refresh failed:', error);
      this.logout();
      return { success: false, error: error.message };
    }
  }
};

// Search actions
export const searchActions = {
  setQuery(query) {
    searchQueryStore.set(query);
  },

  setResults(results) {
    searchResultsStore.set(results);
  },

  setLoading(loading) {
    searchLoadingStore.set(loading);
  },

  updateFilters(filters) {
    const currentFilters = searchFiltersStore.get();
    searchFiltersStore.set({ ...currentFilters, ...filters });
  },

  clearFilters() {
    searchFiltersStore.set({
      minPrice: null,
      maxPrice: null,
      minCapacity: null,
      amenities: [],
      location: null,
      sort: 'relevance'
    });
  }
};

// Notification actions
export const notificationActions = {
  add(notification) {
    const notifications = notificationsStore.get();
    const newNotification = {
      id: Date.now().toString(),
      timestamp: new Date(),
      ...notification
    };
    notificationsStore.set([...notifications, newNotification]);
    
    // Auto-remove after 5 seconds for non-persistent notifications
    if (!notification.persistent) {
      setTimeout(() => {
        this.remove(newNotification.id);
      }, 5000);
    }
  },

  remove(id) {
    const notifications = notificationsStore.get();
    notificationsStore.set(notifications.filter(n => n.id !== id));
  },

  clear() {
    notificationsStore.set([]);
  }
};

// Theme actions
export const themeActions = {
  setTheme(theme) {
    themeStore.set(theme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', theme);
      document.documentElement.setAttribute('data-theme', theme);
    }
  },

  toggleTheme() {
    const currentTheme = themeStore.get();
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  },

  initializeTheme() {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') || 'light';
      this.setTheme(savedTheme);
    }
  }
};

// UI actions
export const uiActions = {
  toggleSidebar() {
    sidebarOpenStore.set(!sidebarOpenStore.get());
  },

  setSidebarOpen(open) {
    sidebarOpenStore.set(open);
  }
};

// Data loading actions
export const dataActions = {
  async loadUserBookings() {
    const user = userStore.get();
    if (!user) return;

    try {
      bookingLoadingStore.set(true);
      const bookings = await pb.collection('bookings').getList(1, 50, {
        filter: `renter.id = "${user.id}" || venue.owner.id = "${user.id}"`,
        expand: 'venue,renter,venue.owner',
        sort: '-created'
      });
      
      activeBookingsStore.set(bookings.items);
      return { success: true, bookings: bookings.items };
    } catch (error) {
      console.error('Failed to load bookings:', error);
      return { success: false, error: error.message };
    } finally {
      bookingLoadingStore.set(false);
    }
  },

  async loadUserVenues() {
    const user = userStore.get();
    if (!user) return;

    try {
      venueLoadingStore.set(true);
      const venues = await pb.collection('venues').getList(1, 50, {
        filter: `owner.id = "${user.id}"`,
        sort: '-created'
      });
      
      userVenuesStore.set(venues.items);
      return { success: true, venues: venues.items };
    } catch (error) {
      console.error('Failed to load venues:', error);
      return { success: false, error: error.message };
    } finally {
      venueLoadingStore.set(false);
    }
  }
};

// Initialize stores on app load
if (typeof window !== 'undefined') {
  // Initialize auth state
  initializeAuth();
  
  // Initialize theme
  themeActions.initializeTheme();
  
  // Listen for auth changes
  pb.authStore.onChange(() => {
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model);
      isAuthenticatedStore.set(true);
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);
    }
  });
}

// Export all stores and actions
export default {
  // Stores
  userStore,
  isAuthenticatedStore,
  authLoadingStore,
  themeStore,
  sidebarOpenStore,
  notificationsStore,
  searchQueryStore,
  searchResultsStore,
  searchLoadingStore,
  searchFiltersStore,
  activeBookingsStore,
  bookingLoadingStore,
  userVenuesStore,
  venueLoadingStore,
  
  // Actions
  authActions,
  searchActions,
  notificationActions,
  themeActions,
  uiActions,
  dataActions
};
