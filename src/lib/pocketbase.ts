import PocketBase from 'pocketbase';

// Get PocketBase URL from environment variables
const POCKETBASE_URL = import.meta.env.POCKETBASE_URL || 'http://localhost:8090';

// Create singleton PocketBase client instance
let _pb: PocketBase | null = null;

export function getPocketBase(): PocketBase {
  if (!_pb) {
    _pb = new PocketBase(POCKETBASE_URL);
    
    // Enable auto cancellation for duplicate requests
    _pb.autoCancellation(false);

    // Configure default settings
    _pb.beforeSend = function (url, options) {
      // Add any default headers or configurations here
      return { url, options };
    };
  }

  return _pb;
}

// Export the singleton instance
export const pocketbase = getPocketBase();
export const pb = pocketbase; // Alias for convenience

// Helper functions for common operations
export async function authenticateUser(email: string, password: string) {
  try {
    const authData = await pocketbase.collection('users').authWithPassword(email, password);
    return { success: true, user: authData.record, token: authData.token };
  } catch (error) {
    console.error('Authentication failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Authentication failed' };
  }
}

export async function registerUser(email: string, password: string, passwordConfirm: string, name?: string) {
  try {
    const userData = {
      email,
      password,
      passwordConfirm,
      name: name || email.split('@')[0], // Use email prefix as default name
    };
    
    const user = await pocketbase.collection('users').create(userData);
    
    // Send verification email
    await pocketbase.collection('users').requestVerification(email);
    
    return { success: true, user };
  } catch (error) {
    console.error('Registration failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Registration failed' };
  }
}

export function logout() {
  pocketbase.authStore.clear();
}

export function getCurrentUser() {
  return pocketbase.authStore.model;
}

export function isAuthenticated(): boolean {
  return pocketbase.authStore.isValid;
}

// Export types for TypeScript support
export type { RecordModel } from 'pocketbase';
export default pocketbase;
