---
import Layout from '../../components/core/Layout.astro';
---

<Layout 
  title="List Your Venue - Trodoo Vibe"
  description="List your property on Trodoo Vibe and start earning from your space."
>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-16 text-center">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            List Your Venue
          </h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Share your space with event hosts and start earning. It's easy to get started.
          </p>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-md p-8">
        <form class="space-y-6">
          <!-- Basic Information -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                  Venue Title *
                </label>
                <input 
                  type="text" 
                  id="title" 
                  name="title"
                  required
                  placeholder="e.g., Beautiful Garden Venue"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              
              <div>
                <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Capacity *
                </label>
                <input 
                  type="number" 
                  id="capacity" 
                  name="capacity"
                  required
                  placeholder="e.g., 100"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            <div class="mt-6">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea 
                id="description" 
                name="description"
                rows="4"
                required
                placeholder="Describe your venue, its features, and what makes it special..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              ></textarea>
            </div>

            <div class="mt-6">
              <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                Address *
              </label>
              <input 
                type="text" 
                id="address" 
                name="address"
                required
                placeholder="Full address of your venue"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <!-- Pricing -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Pricing</h2>
            
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                Price per Hour *
              </label>
              <div class="relative">
                <span class="absolute left-3 top-2 text-gray-500">$</span>
                <input 
                  type="number" 
                  id="price" 
                  name="price"
                  required
                  placeholder="150"
                  class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          <!-- Amenities -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Amenities</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              {['WiFi', 'Parking', 'Kitchen', 'Sound System', 'Projector', 'Air Conditioning', 'Outdoor Space', 'Bar Area', 'Dance Floor'].map(amenity => (
                <label class="flex items-center">
                  <input 
                    type="checkbox" 
                    name="amenities" 
                    value={amenity.toLowerCase().replace(' ', '_')}
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">{amenity}</span>
                </label>
              ))}
            </div>
          </div>

          <!-- Photos -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Photos</h2>
            
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="mt-4">
                <label for="photos" class="cursor-pointer">
                  <span class="mt-2 block text-sm font-medium text-gray-900">
                    Upload venue photos
                  </span>
                  <span class="mt-1 block text-sm text-gray-500">
                    PNG, JPG, GIF up to 10MB each
                  </span>
                  <input id="photos" name="photos" type="file" multiple accept="image/*" class="sr-only" />
                </label>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="pt-6">
            <button 
              type="submit"
              class="w-full bg-primary-600 text-white py-3 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 font-medium"
            >
              List My Venue
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Form submission placeholder
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        // Placeholder for form submission
        console.log('Venue listing form submission will be implemented here');
        alert('Form submission functionality will be implemented with backend integration.');
      });
    }
  });
</script>
