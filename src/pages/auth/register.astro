---
import Layout from '@/components/core/Layout.astro';
---

<Layout 
  title="Create Account - Trodoo Vibe"
  description="Join Trodoo Vibe to discover and book unique venues for your special events."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <a href="/" class="inline-block">
          <h1 class="text-3xl font-bold text-primary-600">Trodoo Vibe</h1>
        </a>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Already have an account?
          <a href="/auth/login" class="font-medium text-primary-600 hover:text-primary-500 ml-1">
            Sign in
          </a>
        </p>
      </div>

      <!-- Registration Form Container -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <div id="register-form-container">
          <!-- React component will be mounted here -->
        </div>
      </div>

      <!-- Terms and Privacy -->
      <div class="text-center">
        <p class="text-xs text-gray-600">
          By creating an account, you agree to our
          <a href="/terms" class="text-primary-600 hover:text-primary-500 mx-1">
            Terms of Service
          </a>
          and
          <a href="/privacy" class="text-primary-600 hover:text-primary-500 ml-1">
            Privacy Policy
          </a>
        </p>
      </div>
    </div>
  </div>
</Layout>

<script>
  // This script will be executed on the client side
  document.addEventListener('DOMContentLoaded', async () => {
    // Dynamic imports for client-side only
    const { default: RegisterForm } = await import('../../components/auth/RegisterForm.tsx');
    const { pb } = await import('../../lib/pocketbase.ts');
    const { userStore } = await import('../../lib/state.js');
    const React = await import('react');
    const ReactDOM = await import('react-dom/client');

    // Mount the React component
    const container = document.getElementById('register-form-container');
    if (container) {
      const handleRegister = async (formData: any) => {
        try {
          // Create the user account
          const _user = await pb.collection('users').create({
            email: formData.email,
            password: formData.password,
            passwordConfirm: formData.passwordConfirm,
            name: formData.name,
            roles: ['renter'], // Default role
          });

          // Send verification email
          await pb.collection('users').requestVerification(formData.email);

          // Auto-login after registration
          const authData = await pb.collection('users').authWithPassword(
            formData.email,
            formData.password
          );

          // Update global state
          userStore.set(authData.record);

          // Redirect to dashboard with welcome message
          window.location.href = '/dashboard?welcome=true';

        } catch (error: any) {
          console.error('Registration failed:', error);

          // Handle specific error cases
          if (error.data?.email) {
            throw new Error('This email is already registered. Please use a different email or sign in.');
          }

          throw new Error(error.message || 'Registration failed. Please try again.');
        }
      };

      // Render the registration form
      const root = ReactDOM.createRoot(container);
      root.render(React.createElement(RegisterForm, { onSubmit: handleRegister }));
    }
  });
</script>
