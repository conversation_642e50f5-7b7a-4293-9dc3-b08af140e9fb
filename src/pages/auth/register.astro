---
import Layout from '../../components/core/Layout.astro';
---

<Layout 
  title="Create Account - Trodoo Vibe"
  description="Join Trodoo Vibe to discover and book unique venues for your special events."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <a href="/" class="inline-block">
          <h1 class="text-3xl font-bold text-primary-600">Trodoo Vibe</h1>
        </a>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Already have an account?
          <a href="/auth/login" class="font-medium text-primary-600 hover:text-primary-500 ml-1">
            Sign in
          </a>
        </p>
      </div>

      <!-- Registration Form Container -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <div id="register-form-container">
          <!-- React component will be mounted here -->
        </div>
      </div>

      <!-- Terms and Privacy -->
      <div class="text-center">
        <p class="text-xs text-gray-600">
          By creating an account, you agree to our
          <a href="/terms" class="text-primary-600 hover:text-primary-500 mx-1">
            Terms of Service
          </a>
          and
          <a href="/privacy" class="text-primary-600 hover:text-primary-500 ml-1">
            Privacy Policy
          </a>
        </p>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Simple registration form without React for now
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('register-form-container');
    if (container) {
      container.innerHTML = `
        <form id="register-form" class="space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your full name"
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Create a password"
            />
          </div>

          <div>
            <label for="passwordConfirm" class="block text-sm font-medium text-gray-700 mb-2">
              Confirm Password
            </label>
            <input
              type="password"
              id="passwordConfirm"
              name="passwordConfirm"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Confirm your password"
            />
          </div>

          <div id="error-message" class="hidden p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600"></p>
          </div>

          <button
            type="submit"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Account
          </button>
        </form>
      `;

      // Handle form submission
      const form = document.getElementById('register-form') as HTMLFormElement;
      if (form) {
        form.addEventListener('submit', (e: Event) => {
          e.preventDefault();

          // Get form data
          const formData = new FormData(form);
          const name = formData.get('name') as string;
          const email = formData.get('email') as string;
          const password = formData.get('password') as string;
          const passwordConfirm = formData.get('passwordConfirm') as string;

          // Simple validation
          if (!name || !email || !password || !passwordConfirm) {
            showError('Please fill in all fields.');
            return;
          }

          if (password !== passwordConfirm) {
            showError('Passwords do not match.');
            return;
          }

          if (password.length < 8) {
            showError('Password must be at least 8 characters long.');
            return;
          }

          // For now, just redirect to dashboard
          // This will be replaced with actual registration
          console.log('Registration attempt:', { name, email, password });
          window.location.href = '/dashboard?welcome=true';
        });
      }
    }

    function showError(message: string) {
      const errorDiv = document.getElementById('error-message');
      if (errorDiv) {
        const errorText = errorDiv.querySelector('p');
        if (errorText) {
          errorText.textContent = message;
          errorDiv.classList.remove('hidden');
        }
      }
    }
  });
</script>
