---
import Layout from '../../components/core/Layout.astro';
---

<Layout 
  title="Sign In - Trodoo Vibe"
  description="Sign in to your Trodoo Vibe account to manage your bookings and venues."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <a href="/" class="inline-block">
          <h1 class="text-3xl font-bold text-primary-600">Trodoo Vibe</h1>
        </a>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Or
          <a href="/auth/register" class="font-medium text-primary-600 hover:text-primary-500 ml-1">
            create a new account
          </a>
        </p>
      </div>

      <!-- Login Form Container -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <div id="login-form-container">
          <!-- React component will be mounted here -->
        </div>
      </div>

      <!-- Additional Links -->
      <div class="text-center space-y-2">
        <a href="/auth/forgot-password" class="text-sm text-primary-600 hover:text-primary-500">
          Forgot your password?
        </a>
        <div class="text-sm text-gray-600">
          Need help? 
          <a href="/contact" class="text-primary-600 hover:text-primary-500 ml-1">
            Contact support
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Simple login form without React for now
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('login-form-container');
    if (container) {
      container.innerHTML = `
        <form id="login-form" class="space-y-6">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your password"
            />
          </div>

          <div id="error-message" class="hidden p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600"></p>
          </div>

          <button
            type="submit"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Sign In
          </button>
        </form>
      `;

      // Handle form submission
      const form = document.getElementById('login-form') as HTMLFormElement;
      if (form) {
        form.addEventListener('submit', (e: Event) => {
          e.preventDefault();

          // Get form data
          const formData = new FormData(form);
          const email = formData.get('email') as string;
          const password = formData.get('password') as string;

          // Simple validation
          if (!email || !password) {
            showError('Please fill in all fields.');
            return;
          }

          // For now, just redirect to dashboard
          // This will be replaced with actual authentication
          console.log('Login attempt:', { email, password });
          window.location.href = '/dashboard';
        });
      }
    }

    function showError(message: string) {
      const errorDiv = document.getElementById('error-message');
      if (errorDiv) {
        const errorText = errorDiv.querySelector('p');
        if (errorText) {
          errorText.textContent = message;
          errorDiv.classList.remove('hidden');
        }
      }
    }
  });
</script>

<style>
  /* Additional styles for the login page */
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.1) 2px, transparent 0),
      radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
  }
</style>
