---
import Layout from '@/components/core/Layout.astro';
---

<Layout 
  title="Sign In - Trodoo Vibe"
  description="Sign in to your Trodoo Vibe account to manage your bookings and venues."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <a href="/" class="inline-block">
          <h1 class="text-3xl font-bold text-primary-600">Trodoo Vibe</h1>
        </a>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Or
          <a href="/auth/register" class="font-medium text-primary-600 hover:text-primary-500 ml-1">
            create a new account
          </a>
        </p>
      </div>

      <!-- Login Form Container -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <div id="login-form-container">
          <!-- React component will be mounted here -->
        </div>
      </div>

      <!-- Additional Links -->
      <div class="text-center space-y-2">
        <a href="/auth/forgot-password" class="text-sm text-primary-600 hover:text-primary-500">
          Forgot your password?
        </a>
        <div class="text-sm text-gray-600">
          Need help? 
          <a href="/contact" class="text-primary-600 hover:text-primary-500 ml-1">
            Contact support
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // This script will be executed on the client side
  document.addEventListener('DOMContentLoaded', async () => {
    // Dynamic imports for client-side only
    const { default: LoginForm } = await import('../../components/auth/LoginForm.tsx');
    const { pb } = await import('../../lib/pocketbase.ts');
    const { userStore } = await import('../../lib/state.js');
    const React = await import('react');
    const ReactDOM = await import('react-dom/client');

    // Mount the React component
    const container = document.getElementById('login-form-container');
    if (container) {
      const handleLogin = async (formData: any) => {
        try {
          const authData = await pb.collection('users').authWithPassword(
            formData.email,
            formData.password
          );

          // Update global state
          userStore.set(authData.record);

          // Redirect to dashboard or intended page
          const urlParams = new URLSearchParams(window.location.search);
          const redirectTo = urlParams.get('redirect') || '/dashboard';
          window.location.href = redirectTo;

        } catch (error: any) {
          console.error('Login failed:', error);
          throw new Error(error.message || 'Login failed. Please try again.');
        }
      };

      // Render the login form
      const root = ReactDOM.createRoot(container);
      root.render(React.createElement(LoginForm, { onSubmit: handleLogin }));
    }
  });
</script>

<style>
  /* Additional styles for the login page */
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.1) 2px, transparent 0),
      radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
  }
</style>
