import { defineMiddleware } from 'astro:middleware';
import { getPocketBase } from './lib/pocketbase.ts';

// Define protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/bookings',
  '/venues/create',
  '/venues/edit',
  '/messages',
  '/settings'
];

// Define admin-only routes
const ADMIN_ROUTES = [
  '/admin',
  '/admin/users',
  '/admin/venues',
  '/admin/reports'
];

// Define routes that should redirect authenticated users (like login/register)
const AUTH_REDIRECT_ROUTES = [
  '/login',
  '/register'
];

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context;
  const pathname = url.pathname;
  
  // Initialize PocketBase client
  const pb = getPocketBase();
  
  // Try to authenticate user from cookies/session
  let isAuthenticated = false;
  let isAdmin = false;
  let user = null;
  
  try {
    // Check if there's an auth token in cookies
    const authCookie = request.headers.get('cookie');
    if (authCookie) {
      // Parse auth token from cookies (this is a simplified example)
      // In a real implementation, you'd want to properly parse and validate the token
      const authMatch = authCookie.match(/pb_auth=([^;]+)/);
      if (authMatch) {
        const authData = JSON.parse(decodeURIComponent(authMatch[1]));
        if (authData.token && authData.model) {
          pb.authStore.save(authData.token, authData.model);
          isAuthenticated = pb.authStore.isValid;
          user = pb.authStore.model;
          
          // Check if user is admin (assuming there's an 'admin' field or role)
          isAdmin = user?.admin === true || user?.role === 'admin';
        }
      }
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    // Clear invalid auth data
    pb.authStore.clear();
  }
  
  // Handle protected routes
  if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      // Redirect to login with return URL
      const returnUrl = encodeURIComponent(pathname + url.search);
      return redirect(`/login?return=${returnUrl}`);
    }
  }
  
  // Handle admin routes
  if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      const returnUrl = encodeURIComponent(pathname + url.search);
      return redirect(`/login?return=${returnUrl}`);
    }
    if (!isAdmin) {
      // Redirect to dashboard or show 403 error
      return redirect('/dashboard?error=unauthorized');
    }
  }
  
  // Handle auth redirect routes (login/register)
  if (AUTH_REDIRECT_ROUTES.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      // Get return URL from query params or default to dashboard
      const returnUrl = url.searchParams.get('return') || '/dashboard';
      return redirect(returnUrl);
    }
  }
  
  // Add user context to locals for use in pages
  context.locals.user = user;
  context.locals.isAuthenticated = isAuthenticated;
  context.locals.isAdmin = isAdmin;
  context.locals.pb = pb;
  
  // Security headers
  const response = await next();
  
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Add CSP header for enhanced security
  const cspDirectives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https:",
    "font-src 'self' https://fonts.gstatic.com",
    "connect-src 'self' " + (import.meta.env.POCKETBASE_URL || 'http://localhost:8090'),
    "frame-ancestors 'none'"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', cspDirectives);
  
  return response;
});

// Type definitions for context.locals
declare global {
  namespace App {
    interface Locals {
      user: any;
      isAuthenticated: boolean;
      isAdmin: boolean;
      pb: any;
    }
  }
}
