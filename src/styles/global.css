@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  body {
    @apply bg-neutral-white text-neutral-black;
    color: #1F2937; /* neutral.black - softer than pure black */
    background-color: #FFFFFF; /* neutral.white */
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .btn-primary {
    background-color: #F59E0B; /* secondary.yellow - refined amber */
    color: #1F2937; /* neutral.black */
    border-radius: 8px; /* md from design system */
    font-weight: 700;
    font-size: 16px;
    padding: 12px 24px;
  }

  .btn-primary:hover {
    background-color: #D97706; /* secondary.yellowDark */
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    background-color: transparent;
    color: #FFFFFF; /* neutral.white */
    border: 2px solid #FFFFFF;
    border-radius: 8px; /* md from design system */
    padding: 10px 22px;
  }

  .btn-secondary:hover {
    background-color: #FFFFFF; /* neutral.white */
    color: #059669; /* primary.green */
  }

  .card {
    background-color: #FFFFFF; /* neutral.white */
    border-radius: 20px; /* xl from design system */
    box-shadow: 0 8px 16px rgba(0,0,0,0.1); /* card shadow */
    border: 1px solid #E5E7EB; /* neutral.mediumGray */
    padding: 32px; /* xl spacing */
  }

  .input {
    @apply w-full;
    border-radius: 8px; /* md from design system */
    border: 1px solid #E5E7EB; /* neutral.mediumGray */
    padding: 12px 16px;
    font-size: 16px;
    background-color: #FFFFFF; /* neutral.white */
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .input:focus {
    outline: none;
    border-color: #059669; /* primary.green */
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1); /* primary green with low opacity */
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
