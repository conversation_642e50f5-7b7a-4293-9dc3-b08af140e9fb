"use client"

import type React from "react"

import type { SpringOptions } from "framer-motion"
import { useRef, useState } from "react"
import { motion, useMotionValue, useSpring } from "framer-motion"

interface SpotlightTiltCardProps extends React.PropsWithChildren {
  className?: string
  spotlightColor?: string
  scaleOnHover?: number
  rotateAmplitude?: number
}

const springValues: SpringOptions = {
  damping: 30,
  stiffness: 100,
  mass: 2,
}

export default function SpotlightTiltCard({
  children,
  className = "",
  spotlightColor = "rgba(255, 255, 255, 0.25)",
  scaleOnHover = 1.02,
  rotateAmplitude = 8,
}: SpotlightTiltCardProps) {
  const ref = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [spotlightOpacity, setSpotlightOpacity] = useState(0)

  const rotateX = useSpring(useMotionValue(0), springValues)
  const rotateY = useSpring(useMotionValue(0), springValues)
  const scale = useSpring(1, springValues)

  function handleMouse(e: React.MouseEvent<HTMLDivElement>) {
    if (!ref.current) return

    const rect = ref.current.getBoundingClientRect()
    const offsetX = e.clientX - rect.left
    const offsetY = e.clientY - rect.top

    const rotationX = (offsetY / (rect.height / 2) - 1) * -rotateAmplitude
    const rotationY = (offsetX / (rect.width / 2) - 1) * rotateAmplitude

    rotateX.set(rotationX)
    rotateY.set(rotationY)
    setPosition({ x: offsetX, y: offsetY })
  }

  function handleMouseEnter() {
    scale.set(scaleOnHover)
    setSpotlightOpacity(0.4)
  }

  function handleMouseLeave() {
    scale.set(1)
    rotateX.set(0)
    rotateY.set(0)
    setSpotlightOpacity(0)
  }

  return (
    <div
      ref={ref}
      onMouseMove={handleMouse}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`relative rounded-2xl border border-white/20 bg-white/10 backdrop-blur-sm overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300 ${className}`}
      style={{
        perspective: "1000px",
        transformStyle: "preserve-3d",
      }}
    >
      <motion.div
        className="relative w-full h-full p-8"
        style={{
          rotateX,
          rotateY,
          scale,
        }}
      >
        <div
          className="pointer-events-none absolute inset-0 transition-opacity duration-500 rounded-2xl"
          style={{
            opacity: spotlightOpacity,
            background: `radial-gradient(circle at ${position.x}px ${position.y}px, ${spotlightColor}, transparent 70%)`,
          }}
        />
        <div className="relative z-10 h-full flex flex-col items-center text-center">{children}</div>
      </motion.div>
    </div>
  )
}
