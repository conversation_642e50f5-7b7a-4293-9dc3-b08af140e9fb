import React, { useState } from 'react';
import type { Booking } from '../../types/booking.ts';
import { CompactStarRating } from '../common/StarRating.tsx';
import Button from '../common/Button.tsx';

interface BookingListProps {
  bookings: Booking[];
  userRole: 'renter' | 'owner';
  onBookingAction?: (bookingId: string, action: string) => void;
  isLoading?: boolean;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  paid: 'bg-green-100 text-green-800',
  completed: 'bg-gray-100 text-gray-800',
  cancelled: 'bg-red-100 text-red-800',
};

const statusLabels = {
  pending: 'Pending Approval',
  confirmed: 'Confirmed',
  paid: 'Paid',
  completed: 'Completed',
  cancelled: 'Cancelled',
};

export default function BookingList({ 
  bookings, 
  userRole, 
  onBookingAction,
  isLoading = false 
}: BookingListProps) {
  const [filter, setFilter] = useState<string>('all');

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    return booking.status === filter;
  });

  const handleAction = (bookingId: string, action: string) => {
    if (onBookingAction) {
      onBookingAction(bookingId, action);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getActionButtons = (booking: Booking) => {
    const buttons = [];

    if (userRole === 'owner' && booking.status === 'pending') {
      buttons.push(
        <Button
          key="approve"
          size="sm"
          onClick={() => handleAction(booking.id, 'approve')}
        >
          Approve
        </Button>
      );
      buttons.push(
        <Button
          key="reject"
          size="sm"
          variant="outline"
          onClick={() => handleAction(booking.id, 'reject')}
        >
          Reject
        </Button>
      );
    }

    if (userRole === 'renter' && booking.status === 'confirmed') {
      buttons.push(
        <Button
          key="pay"
          size="sm"
          onClick={() => handleAction(booking.id, 'pay')}
        >
          Pay Now
        </Button>
      );
    }

    buttons.push(
      <Button
        key="view"
        size="sm"
        variant="ghost"
        onClick={() => handleAction(booking.id, 'view')}
      >
        View Details
      </Button>
    );

    return buttons;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {['all', 'pending', 'confirmed', 'paid', 'completed'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                filter === status
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {status === 'all' ? 'All Bookings' : statusLabels[status as keyof typeof statusLabels]}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {status === 'all' 
                  ? bookings.length 
                  : bookings.filter(b => b.status === status).length
                }
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Booking Cards */}
      {filteredBookings.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No bookings found</div>
          <p className="text-gray-600">
            {filter === 'all' 
              ? 'You don\'t have any bookings yet.' 
              : `No ${filter} bookings at the moment.`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map((booking) => (
            <div
              key={booking.id}
              className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {booking.venue.title}
                    </h3>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        statusColors[booking.status as keyof typeof statusColors]
                      }`}
                    >
                      {statusLabels[booking.status as keyof typeof statusLabels]}
                    </span>
                  </div>

                  <div className="text-sm text-gray-600 space-y-1">
                    <p>
                      <span className="font-medium">Event Date:</span>{' '}
                      {formatDate(booking.start_date)} - {formatDate(booking.end_date)}
                    </p>
                    <p>
                      <span className="font-medium">
                        {userRole === 'owner' ? 'Renter:' : 'Venue Owner:'}
                      </span>{' '}
                      {userRole === 'owner' ? booking.renter.name : booking.venue.owner.name}
                    </p>
                    <p>
                      <span className="font-medium">Total:</span>{' '}
                      <span className="text-lg font-semibold text-gray-900">
                        ${booking.total_price.toLocaleString()}
                      </span>
                    </p>
                  </div>

                  {booking.venue.average_rating && (
                    <div className="mt-3">
                      <CompactStarRating 
                        rating={booking.venue.average_rating} 
                        reviewCount={booking.venue.review_count}
                      />
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  {getActionButtons(booking)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
