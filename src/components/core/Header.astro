---
// Header component for site navigation
---

<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <h1 class="text-2xl font-bold text-primary-600">Trodoo Vibe</h1>
        </a>
      </div>

      <!-- Navigation -->
      <nav class="hidden md:flex space-x-8">
        <a href="/" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium" data-nav-link>
          Home
        </a>
        <a href="/venues" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium" data-nav-link>
          Browse Venues
        </a>
        <a href="/venues/new" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium" data-nav-link>
          List Your Venue
        </a>
        <a href="/dashboard" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium" data-nav-link>
          Dashboard
        </a>
      </nav>

      <!-- Auth Buttons -->
      <div class="flex items-center space-x-4">
        <div id="auth-buttons" class="flex items-center space-x-4">
          <!-- Default logged-out state -->
          <a href="/auth/login" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
            Sign In
          </a>
          <a href="/auth/register" class="bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium">
            Sign Up
          </a>
        </div>

        <!-- Mobile menu button -->
        <button type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" id="mobile-menu-button">
          <span class="sr-only">Open main menu</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="md:hidden hidden" id="mobile-menu">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="/" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Home</a>
        <a href="/venues" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Browse Venues</a>
        <a href="/venues/new" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">List Your Venue</a>
        <a href="/dashboard" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Dashboard</a>
        <div class="border-t border-gray-200 pt-4 pb-3">
          <a href="/auth/login" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Sign In</a>
          <a href="/auth/register" class="bg-primary-600 text-white hover:bg-primary-700 block px-3 py-2 rounded-md text-base font-medium">Sign Up</a>
        </div>
      </div>
    </div>
  </div>
</header>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', () => {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Update auth buttons based on authentication state
    // This will be enhanced when we integrate with the auth system
    const authButtons = document.getElementById('auth-buttons');
    if (authButtons) {
      // Check if user is authenticated (placeholder for now)
      const isAuthenticated = false; // This will be replaced with actual auth check

      if (isAuthenticated) {
        authButtons.innerHTML = `
          <a href="/dashboard" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
            Dashboard
          </a>
          <button id="logout-btn" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
            Sign Out
          </button>
        `;
      }
    }
  });
</script>
