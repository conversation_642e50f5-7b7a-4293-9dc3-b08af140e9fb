---
// Main layout component that wraps page content
import Head<PERSON> from './Header.astro';
import Footer from './Footer.astro';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}

const {
  title,
  description = 'Discover and book unique venues for your special events with Trodoo Vibe.',
  image = '/images/og-default.jpg',
  noIndex = false,
  showHeader = true,
  showFooter = true,
  className = '',
} = Astro.props;

const siteURL = Astro.site || new URL('http://localhost:4322');
const canonicalURL = new URL(Astro.url.pathname, siteURL);
const imageURL = image.startsWith('http') ? image : new URL(image, siteURL).href;
---

<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- SEO Meta Tags -->
    <title>{title}</title>
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={imageURL} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={imageURL} />
    
    <!-- Robots -->
    {noIndex && <meta name="robots" content="noindex, nofollow" />}
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#059669" />
  </head>
  
  <body class={`h-full bg-white text-gray-900 ${className}`}>
    <!-- Skip to main content for accessibility -->
    <a 
      href="#main-content" 
      class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50"
    >
      Skip to main content
    </a>
    
    <!-- Header -->
    {showHeader && <Header />}
    
    <!-- Main Content -->
    <main id="main-content" class="flex-1">
      <slot />
    </main>
    
    <!-- Footer -->
    {showFooter && <Footer />}
    
    <!-- Scripts -->
    <script>
      // Initialize theme handling
      const theme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', theme);
      
      // Handle navigation state
      window.addEventListener('load', () => {
        // Mark current page in navigation
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('[data-nav-link]');
        navLinks.forEach(link => {
          if (link.getAttribute('href') === currentPath) {
            link.setAttribute('aria-current', 'page');
            link.classList.add('active');
          }
        });
      });
    </script>
  </body>
</html>

<style is:global>
  /* Global styles */
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: theme('colors.gray.100');
  }
  
  ::-webkit-scrollbar-thumb {
    background: theme('colors.gray.400');
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: theme('colors.gray.500');
  }
  
  /* Print styles */
  @media print {
    header, footer, nav {
      display: none !important;
    }
    
    main {
      margin: 0 !important;
      padding: 0 !important;
    }
  }
</style>
