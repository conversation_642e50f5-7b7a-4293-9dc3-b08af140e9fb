import React from 'react';
import type { Venue } from '../../types/venue.ts';
import { CompactStarRating } from '../common/StarRating.tsx';
import Button from '../common/Button.tsx';

interface VenueCardProps {
  venue: Venue;
  onBookNow?: (venueId: string) => void;
  onViewDetails?: (venueId: string) => void;
  showActions?: boolean;
  className?: string;
}

export default function VenueCard({ 
  venue, 
  onBookNow, 
  onViewDetails,
  showActions = true,
  className = '' 
}: VenueCardProps) {
  const handleBookNow = () => {
    if (onBookNow) {
      onBookNow(venue.id);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(venue.id);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${className}`}>
      {/* Image */}
      <div className="relative h-48 bg-gray-200">
        {venue.photos && venue.photos.length > 0 ? (
          <img
            src={venue.photos[0]}
            alt={venue.title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
        
        {/* 360° Badge */}
        {venue.photo_360 && (
          <div className="absolute top-3 left-3">
            <span className="bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full">
              360° View
            </span>
          </div>
        )}

        {/* Price Badge */}
        <div className="absolute top-3 right-3">
          <span className="bg-white text-gray-900 text-sm font-semibold px-3 py-1 rounded-full shadow-md">
            {formatPrice(venue.price_per_hour)}/hr
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {venue.title}
          </h3>
          <p className="text-sm text-gray-600 line-clamp-1">
            {venue.address}
          </p>
        </div>

        <p className="text-sm text-gray-700 line-clamp-2 mb-3">
          {venue.description}
        </p>

        {/* Amenities */}
        {venue.amenities && venue.amenities.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {venue.amenities.slice(0, 3).map((amenity, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {amenity}
                </span>
              ))}
              {venue.amenities.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  +{venue.amenities.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Capacity */}
        <div className="flex items-center text-sm text-gray-600 mb-3">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          Up to {venue.capacity} guests
        </div>

        {/* Rating */}
        {venue.average_rating && (
          <div className="mb-4">
            <CompactStarRating 
              rating={venue.average_rating} 
              reviewCount={venue.review_count}
            />
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="flex-1"
            >
              View Details
            </Button>
            <Button
              size="sm"
              onClick={handleBookNow}
              className="flex-1"
            >
              Book Now
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

// Skeleton loader for venue cards
export function VenueCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
      <div className="h-48 bg-gray-200"></div>
      <div className="p-4">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
        <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3 mb-3"></div>
        <div className="flex gap-1 mb-3">
          <div className="h-6 bg-gray-200 rounded-full w-16"></div>
          <div className="h-6 bg-gray-200 rounded-full w-20"></div>
          <div className="h-6 bg-gray-200 rounded-full w-14"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded w-1/3 mb-3"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="flex gap-2">
          <div className="h-8 bg-gray-200 rounded flex-1"></div>
          <div className="h-8 bg-gray-200 rounded flex-1"></div>
        </div>
      </div>
    </div>
  );
}
