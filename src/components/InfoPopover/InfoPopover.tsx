"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Info, X } from "lucide-react"
import AnimatedList from "../AnimatedList/AnimatedList.tsx"
import GlassIcons from "../GlassIcons/GlassIcons.tsx"

interface InfoPopoverProps {
  content: string
}

export default function InfoPopover({ content }: InfoPopoverProps) {
  const [isOpen, setIsOpen] = useState(false)
  const popoverRef = useRef<HTMLDivElement>(null)
  const listItems = content.split('. ').filter(item => item);

  const togglePopover = () => setIsOpen(!isOpen)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  return (
    <div className="relative flex justify-center my-4" ref={popoverRef}>
      <div onClick={togglePopover}>
        <GlassIcons
          items={[
            {
              icon: isOpen ? <X size={32} /> : <Info size={32} />,
              color: "secondary",
              label: "Info",
            },
          ]}
          className="w-24"
        />
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-full mb-3 w-72 p-4 bg-white/90 backdrop-blur-lg rounded-xl shadow-2xl border border-white/20"
          >
            <div className="text-gray-800">
              <AnimatedList
                items={listItems}
                className="w-full"
                itemClassName="text-sm"
                displayScrollbar={false}
                showGradients={false}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}